apiVersion: v1
kind: ServiceAccount
metadata:
  name: abun-drf-service-account
  namespace: abun-staging

---

apiVersion: v1
kind: ServiceAccount
metadata:
  name: abun-artgen-service-account
  namespace: abun-staging

---

apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: abun-drf-role
  namespace: abun-staging
rules:
  - apiGroups:
        - ""
        - apps
        - autoscaling
        - batch
        - extensions
        - policy
        - rbac.authorization.k8s.io
    resources:
      - pods
      - componentstatuses
      - configmaps
      - daemonsets
      - deployments
      - events
      - endpoints
      - horizontalpodautoscalers
      - ingress
      - jobs
      - limitranges
      - namespaces
      - nodes
      - pods
      - persistentvolumes
      - persistentvolumeclaims
      - resourcequotas
      - replicasets
      - replicationcontrollers
      - serviceaccounts
      - services
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: abun-artgen-clusterrole
  namespace: abun-staging
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - pods
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: abun-drf-service-account-clusterrole
  namespace: abun-staging
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - pods
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: abun-drf-rolebinding
  namespace: abun-staging
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: abun-drf-role
subjects:
- namespace: abun-staging
  kind: ServiceAccount
  name: abun-drf-service-account

---

apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: abun-artgen-rolebinding
  namespace: abun-staging
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: abun-drf-role
subjects:
- namespace: abun-staging
  kind: ServiceAccount
  name: abun-artgen-service-account

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: abun-artgen-clusterrolebinding
  namespace: abun-staging
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: abun-artgen-clusterrole
subjects:
- namespace: abun-staging
  kind: ServiceAccount
  name: abun-artgen-service-account

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: abun-drf-service-account-clusterrolebinding
  namespace: abun-staging
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: abun-drf-service-account-clusterrole
subjects:
- namespace: abun-staging
  kind: ServiceAccount
  name: abun-drf-service-account

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-drf-deployment
  namespace: abun-staging
  labels:
    app: abun-drf
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-drf
  template:
    metadata:
      labels:
        app: abun-drf
    spec:
      serviceAccountName: abun-drf-service-account
      containers:
      - name: abun-drf-container
        image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.80.0b1
        imagePullPolicy: Always
        tty: true
        stdin: true
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        ports:
        - containerPort: 8000
        volumeMounts:
        - name: chromadb-store-volume
          mountPath: abun_chroma_db
      volumes:
      - name: chromadb-store-volume
        persistentVolumeClaim:
          claimName: chromadb-store-volume-claim
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

# apiVersion: apps/v1
# kind: Deployment
# metadata:
#   name: abun-content-automation-deployment
#   namespace: abun-staging
#   labels:
#     app: abun-content-automation
# spec:
#   replicas: 1
#   selector:
#     matchLabels:
#       app: abun-content-automation
#   template:
#     metadata:
#       labels:
#         app: abun-content-automation
#     spec:
#       serviceAccountName: abun-artgen-service-account
#       containers:
#       - name: abun-content-automation-container
#         image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.80.0b1
#         command: ["python", "manage.py", "content_automation"]
#         imagePullPolicy: Always
#         tty: true
#         stdin: true
#         envFrom:
#           - secretRef:
#               name: abun-app-env-variables
#         volumeMounts:
#         - name: chromadb-store-volume
#           mountPath: abun_chroma_db
#       volumes:
#       - name: chromadb-store-volume
#         persistentVolumeClaim:
#           claimName: chromadb-store-volume-claim
#       imagePullSecrets:
#        - name: gitlab-registry-credentials

# ---

# apiVersion: apps/v1
# kind: Deployment
# metadata:
#   name: abun-website-scanning-queue-processor-deployment
#   namespace: abun-staging
#   labels:
#     app: abun-website-scanning-queue-processor
# spec:
#   replicas: 1
#   selector:
#     matchLabels:
#       app: abun-website-scanning-queue-processor
#   template:
#     metadata:
#       labels:
#         app: abun-website-scanning-queue-processor
#     spec:
#       serviceAccountName: abun-drf-service-account
#       containers:
#       - name: abun-website-scanning-queue-processor-container
#         image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.80.0b1
#         command: ["python", "manage.py", "process_website_scanning_queue"]
#         imagePullPolicy: Always
#         envFrom:
#           - secretRef:
#               name: abun-app-env-variables
#         volumeMounts:
#         - name: chromadb-store-volume
#           mountPath: abun_chroma_db
#       volumes:
#       - name: chromadb-store-volume
#         persistentVolumeClaim:
#           claimName: chromadb-store-volume-claim
#       imagePullSecrets:
#        - name: gitlab-registry-credentials

# ---

# apiVersion: apps/v1
# kind: Deployment
# metadata:
#   name: abun-article-generation-queue-processor-deployment
#   namespace: abun-staging
#   labels:
#     app: abun-article-generation-queue-processor
# spec:
#   replicas: 1
#   selector:
#     matchLabels:
#       app: abun-article-generation-queue-processor
#   template:
#     metadata:
#       labels:
#         app: abun-article-generation-queue-processor
#     spec:
#       serviceAccountName: abun-drf-service-account
#       containers:
#       - name: abun-article-generation-queue-processor-container
#         image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.80.0b1
#         command: ["python", "manage.py", "process_article_generation_queue"]
#         imagePullPolicy: Always
#         envFrom:
#           - secretRef:
#               name: abun-app-env-variables
#         volumeMounts:
#         - name: chromadb-store-volume
#           mountPath: abun_chroma_db
#       volumes:
#       - name: chromadb-store-volume
#         persistentVolumeClaim:
#           claimName: chromadb-store-volume-claim
#       imagePullSecrets:
#        - name: gitlab-registry-credentials

# ---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-celery-deployment
  namespace: abun-staging
  labels:
    app: abun-celery
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-celery
  template:
    metadata:
      labels:
        app: abun-celery
    spec:
      serviceAccountName: abun-drf-service-account
      containers:
      - name: abun-celery-container
        image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.80.0b1
        command: ["celery", "-A", "AbunDRFBackend", "worker", "-l", "INFO"]
        imagePullPolicy: Always
        tty: true
        stdin: true
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        volumeMounts:
        - name: chromadb-store-volume
          mountPath: abun_chroma_db
      volumes:
      - name: chromadb-store-volume
        persistentVolumeClaim:
          claimName: chromadb-store-volume-claim
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-react-deployment
  namespace: abun-staging
  labels:
    app: abun-react
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-react
  template:
    metadata:
      labels:
        app: abun-react
    spec:
      containers:
      - name: abun-react-container
        image: registry.gitlab.com/aminmemon/abun-react-frontend/abun-react-frontend-staging:v3.79.0b1
        tty: true
        stdin: true
        imagePullPolicy: Always
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        ports:
        - containerPort: 3000
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-admin-deployment
  namespace: abun-staging
  labels:
    app: abun-admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-admin
  template:
    metadata:
      labels:
        app: abun-admin
    spec:
      containers:
      - name: abun-admin-container
        image: registry.gitlab.com/aminmemon/abun-admin-react/abun-admin-staging:v2.40.0b1
        tty: true
        stdin: true
        imagePullPolicy: Always
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        ports:
        - containerPort: 3000
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: v1
kind: Service
metadata:
  name: abun-react-service
  namespace: abun-staging
spec:
  ports:
    - name: http
      port: 3000
      targetPort: 3000
  selector:
    app: abun-react

---

apiVersion: v1
kind: Service
metadata:
  name: abun-drf-service
  namespace: abun-staging
spec:
  ports:
    - name: http
      port: 8000
      targetPort: 8000
  selector:
    app: abun-drf

---

apiVersion: v1
kind: Service
metadata:
  name: abun-admin-service
  namespace: abun-staging
spec:
  ports:
    - name: http
      port: 3001
      targetPort: 3001
  selector:
    app: abun-admin
