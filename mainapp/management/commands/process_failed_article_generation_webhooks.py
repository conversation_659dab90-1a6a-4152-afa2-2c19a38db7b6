import json
import logging
import datetime
import re
from typing import Dict, List, Any
from zoneinfo import ZoneInfo

from django.core.management.base import BaseCommand
from langchain.schema import exceptions

from mainapp.models import (KubernetesJob, Article, User, AllArticlesStats, AIArticleImage)
from mainapp.utils import (get_redis_connection, save_featured_image, get_unsplash_images,
                           get_word_count, sanitize_url_slug, generate_AI_feature_image__sync,
                           get_table_of_content, format_toc_markdown, add_heading_ids, send_email)
from mainapp.custom_featured_images import generate_custom_feature_image
from mainapp.email_messages import article_generation_success_email_body, article_generation_failed_email_body_for_admins
from mainapp.words_translations import get_faq_translation
from AbunDRFBackend.settings import (REDIS_TASK_DATA_DB, ABUN_NOTIFICATION_EMAIL,
                                     ADIL_EMAIL, AMIN_EMAIL, DEBUG, K8_JOB_RETRIES)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Process failed article generation webhook data from Redis and store to database'

    def handle(self, *_args, **_options):
        """
        Main handler that processes failed article generation webhook data from Redis.
        This command is designed to run as a cronjob every minute.
        """
        self.stdout.write('Checking for failed article generation webhook data in Redis...')

        try:
            self.process_failed_webhooks()
        except Exception as e:
            logger.critical(f"Error processing failed article generation webhooks: {str(e)}")
            self.stdout.write(self.style.ERROR(f'Error processing failed webhooks: {str(e)}'))

    def process_failed_webhooks(self):
        """
        Check Redis for failed webhook data and process each entry.
        """
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            # Check if the key exists
            failed_data = redis_connection.get('article_generation_data')

            if not failed_data:
                self.stdout.write('No failed article generation data found in Redis.')
                return

            try:
                # Parse the JSON data
                failed_webhooks: List[Dict] = json.loads(failed_data)
                self.stdout.write(f'Found {len(failed_webhooks)} failed webhook(s) to process.')

                # Get all unique job IDs from the failed webhooks
                unique_job_ids = set(webhook['job_id'] for webhook in failed_webhooks)
                unique_failed_webhooks = [webhook for webhook in failed_webhooks if webhook['job_id'] in unique_job_ids]

                # Process each failed webhook
                processed_count = 0
                for webhook_data in unique_failed_webhooks:
                    try:
                        if self.process_single_webhook(webhook_data):
                            processed_count += 1
                    except Exception as e:
                        logger.critical(f"Error processing single webhook: {str(e)}")
                        self.stdout.write(self.style.ERROR(f'Error processing webhook: {str(e)}'))
                        continue

                # If all webhooks were processed successfully, delete the Redis key
                if processed_count == len(failed_webhooks):
                    redis_connection.delete('article_generation_data')
                    self.stdout.write(self.style.SUCCESS(f'Successfully processed all {processed_count} failed webhooks and cleared Redis key.'))
                else:
                    self.stdout.write(self.style.WARNING(f'Processed {processed_count} out of {len(failed_webhooks)} webhooks. Some failed - keeping Redis key.'))

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON data from Redis: {str(e)}")
                self.stdout.write(self.style.ERROR(f'Failed to parse JSON data: {str(e)}'))

            except Exception as e:
                logger.critical(f"Unexpected error processing failed webhooks: {str(e)}")
                self.stdout.write(self.style.ERROR(f'Unexpected error: {str(e)}'))

    def process_single_webhook(self, webhook_data: Dict[str, Any]) -> bool:
        """
        Process a single failed webhook data entry.
        
        Args:
            webhook_data: Dictionary containing the webhook data
            
        Returns:
            bool: True if processed successfully, False otherwise
        """
        try:
            event_type = webhook_data.get('event')
            job_id = webhook_data.get('job_id')
            
            if not event_type or not job_id:
                logger.error(f"Missing event_type or job_id in webhook data: {webhook_data}")
                return False
            
            # Get the KubernetesJob
            try:
                task = KubernetesJob.objects.get(job_id=job_id)
                user: User = task.user
            except KubernetesJob.DoesNotExist:
                logger.error(f"Kubernetes Job {job_id} does not exist.")
                return False
            
            # Process based on event type
            if event_type == 'article_generation_v2_completed':
                return self.process_article_generation_success(webhook_data, task, user)

            elif event_type == 'article_generation_failed':
                return self.process_article_generation_failure(webhook_data, task, user)

            else:
                logger.error(f"Unrecognized event type: {event_type}")
                return False
                
        except Exception as e:
            logger.critical(f"Error processing single webhook: {str(e)}")
            return False

    def process_article_generation_success(self, webhook_data: Dict[str, Any], task: KubernetesJob, user: User) -> bool:
        """
        Process successful article generation webhook data.
        This replicates the logic from the original webhook handler.
        """
        try:
            article_data: Dict = webhook_data['data']['article_data']
            article_uid: str = webhook_data['data']['article_uid']

            # Update task with cost info
            task.article_generation_cost_info = webhook_data['data']['cost_info']
            task.save()

            # Get the article
            try:
                article: Article = Article.objects.get(article_uid=article_uid)
            except Article.DoesNotExist:
                logger.error(f"Article {article_uid} does not exist.")
                return False

            # Update article with generation data
            article.summary_inputs = article_data.get('summary_inputs', '').replace("\x00", "\uFFFD")
            article.merged_summary = article_data.get('merged_summary', '').replace("\x00", "\uFFFD")
            article.article_outlines = article_data.get('outline_generation', [])
            article.crewai_output = article_data.get('crewai_verbose', '').replace("\x00", "\uFFFD")
            article.url_slug = sanitize_url_slug(article.title.lower().replace(" ","-")[:50])

            # Process article content and images
            self._process_article_content_and_images(article, article_data, user)

            # Mark article as completed
            article.is_processing = False
            article.is_generated = True
            article.generated_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
            article.save()

            # Update task status
            task.status = 'completed'
            task.save()

            # Update stats
            AllArticlesStats.objects.update_or_create(
                article_uid=article_uid,
                defaults={
                    'generated_on': datetime.datetime.now(tz=ZoneInfo('UTC')),
                    'is_successful': True
                }
            )

            # Send success email if enabled
            if user.send_notification_emails:
                email_message: str = article_generation_success_email_body(user.username, article_uid)
                subject: str = "Your Generated Article is Ready"
                send_email(user.email, ABUN_NOTIFICATION_EMAIL, "Team Abun", subject, email_message)

            # Delete the redis task data key
            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                redis_connection.delete(task.job_id)

            logger.info(f"Successfully processed article generation success for {article_uid}")
            self.stdout.write(self.style.SUCCESS(f'Processed successful article generation for {article_uid}'))
            return True

        except Exception as e:
            logger.critical(f"Error processing article generation success: {str(e)}")
            return False

    def process_article_generation_failure(self, webhook_data: Dict[str, Any], task: KubernetesJob, user: User) -> bool:
        """
        Process failed article generation webhook data.
        This replicates the logic from the original webhook handler.
        """
        try:
            error_message = webhook_data.get('error_message', 'Unknown error')

            # Update task
            task.status = 'failed'
            task.fail_reason = error_message
            task.retry_attempts += 1

            if task.retry_attempts >= K8_JOB_RETRIES:
                stage_data: Dict = webhook_data.get('stage_data', {})
                article_uid: str = task.metadata

                try:
                    article = Article.objects.get(article_uid=article_uid)
                    article.is_processing = False
                    article.is_failed = True
                    article.summary_inputs = stage_data.get('summary_inputs', '').replace("\x00", "\uFFFD")
                    article.merged_summary = stage_data.get('merged_summary', '').replace("\x00", "\uFFFD")
                    article.article_outlines = stage_data.get('outline_generation', [])
                    article.save()

                    # Update user article count
                    user.articles_generated -= 1
                    user.total_articles_generated -= 1
                    user.save()

                    # Update stats
                    AllArticlesStats.objects.update_or_create(
                        article_uid=article_uid,
                        defaults={
                            'generated_on': datetime.datetime.now(tz=ZoneInfo('UTC')),
                            'is_successful': False
                        }
                    )

                    # Send failure email to admins if not in debug mode
                    if not DEBUG:
                        failed_article_email_message_for_admins = article_generation_failed_email_body_for_admins(user, article, error_message)

                        send_email(
                            [ADIL_EMAIL, AMIN_EMAIL],
                            ABUN_NOTIFICATION_EMAIL,
                            "Team Abun",
                            f"Article Generation Failed for {user.email}",
                            failed_article_email_message_for_admins,
                            reply_to=user.email
                        )

                    # Delete the redis task data key
                    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                        redis_connection.delete(task.job_id)

                except Article.DoesNotExist:
                    logger.error(f"Article {article_uid} does not exist.")
                    return False

            task.save()
            logger.info(f"Successfully processed article generation failure for job {task.job_id}")
            self.stdout.write(self.style.SUCCESS(f'Processed failed article generation for job {task.job_id}'))
            return True

        except Exception as e:
            logger.critical(f"Error processing article generation failure: {str(e)}")
            return False

    def _process_article_content_and_images(self, article: Article, article_data: Dict, user: User):
        """
        Process article content and images. This replicates the complex logic from the original webhook.
        """
        try:
            # Assemble the main article body from body_content
            assembled_body: str = ""
            body_content = article_data.get('body_content', [])

            for index, section in enumerate(body_content or []):  # [{heading: str, paragraphs: List[str]}, ...]
                # Only process sections that have both heading and paragraphs
                if not section.get('heading') or not section.get('paragraphs'):
                    continue

                # Filter out empty paragraphs
                valid_paragraphs = [p.strip() for p in section['paragraphs'] if p]
                if not valid_paragraphs:
                    continue

                # Assemble all section paragraphs and mark important sentences/phrases as bold.
                assembled_paragraphs: str = "\n\n".join(valid_paragraphs)
                for text in section.get('bold', []):
                    if text and text.strip():  # Only process non-empty bold text
                        match = re.search(text, assembled_paragraphs)
                        if match:
                            match_text: str = str(match.group())
                            # Avoid bolding text within an anchor tag
                            if f'<a href="{match_text}"' not in assembled_paragraphs:
                                assembled_paragraphs = re.sub(
                                    text, f"**{match_text}**", assembled_paragraphs, count=1, flags=re.IGNORECASE
                                )

                # Add section heading and content
                assembled_body += f"## {section['heading']}\n\n{assembled_paragraphs}\n\n"

                # Handle bullet points if they exist for this section
                bullet_points = article_data.get('bullet_points', {})
                if isinstance(bullet_points, dict) and section["heading"] in bullet_points.get("section_heading", []):
                    bullet_list = bullet_points.get('bulleted_list_markdown', '').strip()
                    if bullet_list:  # Only add if bullet list is not empty
                        assembled_body += f"{bullet_list}\n\n"

            # Ensure no extra newlines at the end of the article body
            assembled_body = assembled_body.strip()

            # Add images between sub heading and subheading content - only valid images
            total_image_count: int = 0
            ai_image_objects = []
            for image in article_data.get('image_suggestions', []):
                # Skip if no image source or explicitly marked as no image
                if not image.get('image_source') or image['image_source'] == 'no_image':
                    continue

                # Skip if no valid image URL
                image_url = image.get('image_url', '').strip()
                if not image_url:
                    continue

                # Process images and create AIArticleImage objects only for valid images
                # AIArticleImage only has 3 fields: image_url, context_title, generated_context_description
                context_title = image.get('main', '').strip() or image.get('image_title', '').strip() or 'Article Image'
                context_description = image.get('image_context_description', '').strip() or image.get('image_description', '').strip() or 'Generated image for article'

                ai_article_image = AIArticleImage(
                    image_url=image_url,
                    context_title=context_title,
                    generated_context_description=context_description,
                )
                ai_image_objects.append(ai_article_image)
                total_image_count += 1

            if ai_image_objects:
                AIArticleImage.objects.bulk_create(ai_image_objects, batch_size=100)

            article.image_count = total_image_count

            # Assemble the F.A.Q section only if FAQ data exists and is valid
            assembled_faq = ""
            faq_data = article_data.get('faq', [])
            if faq_data and len(faq_data) > 0 and (not DEBUG or user.toggle_faq):
                # Check if FAQ items have both question and answer
                valid_faq_items = [item for item in faq_data if item.get('question') and item.get('answer')]
                if valid_faq_items:
                    assembled_faq = "## " + get_faq_translation(user.article_language_preference)
                    for index, item in enumerate(valid_faq_items):
                        assembled_faq += f"\n\n#### {index + 1}. {item['question']}\n{item['answer']}"

            # Get TLDR content only if it exists and is not empty
            tldr_content = article_data.get('tldr', '')
            tldr_content = tldr_content.strip() if tldr_content else ''

            # Assemble complete article markdown with only valid content
            article_parts = [assembled_body]

            if assembled_faq:
                article_parts.append(assembled_faq)

            if tldr_content:
                article_parts.append(tldr_content)

            article_markdown: str = "\n\n".join(article_parts)

            article_markdown = re.sub(r'\s*—\s*', ', ', article_markdown)

            # Get intro paragraph only if it exists and is not empty
            intro_para = article_data.get('intro_para', '').strip()
            article_intro = ""
            if intro_para:
                article_intro = re.sub(r'\s*—\s*', ', ', intro_para)

            # Generate table of contents only if there's content and user has it enabled
            toc_data = None
            toc_markdown = ""
            if article_markdown.strip() and (not DEBUG or user.toggle_toc):
                try:
                    toc_data = get_table_of_content(user.article_language_preference, article_markdown)
                    if toc_data:
                        toc_markdown = format_toc_markdown(toc_data)
                except exceptions.OutputParserException:
                    logger.error("Failed to parse table of content")
                    toc_data = None
                except Exception as err:
                    logger.critical(f"Failed to generate table of content: {err}")
                    toc_data = None

            # Combine content with TOC and intro - only include non-empty parts
            content_parts = []

            if article_intro:
                content_parts.append(article_intro)

            if toc_markdown:
                content_parts.append(toc_markdown)

            if article_markdown.strip():
                if toc_data:  # If TOC was generated, add heading IDs
                    updated_markdown_content = add_heading_ids(article_markdown)
                    content_parts.append(updated_markdown_content)
                else:
                    content_parts.append(article_markdown)

            complete_article_markdown = "\n\n".join(content_parts)

            # Only update article if we have meaningful content
            if not complete_article_markdown.strip():
                logger.warning(f"Article {article.article_uid} has no content to save. Skipping content update.")
                return  # Don't process further if there's no content

            # Update article with content and metadata
            article_description = article_data.get("article_description", "")
            if article_description:
                article.article_description = article_description.replace("\x00", "\uFFFD")

            article.word_count = get_word_count(complete_article_markdown)
            article.content = complete_article_markdown.replace("\x00", "\uFFFD")

            # Process featured image
            feature_image = article.selected_featured_image
            selected_template = user.feature_image_template_id

            if feature_image and feature_image.template_image_url:
                template_photo = feature_image.template_image_url

                # Generate custom featured image
                if selected_template and selected_template != "no_template":
                    try:
                        custom_image_url = generate_custom_feature_image(
                            article.title,
                            template_photo,
                            selected_template,
                            user.feature_image_template_label
                        )
                        if custom_image_url:
                            save_featured_image(custom_image_url, "bannerbear", article, selected_template, template_photo)

                    except Exception as e:
                        logger.critical(f"Error generating custom featured image: {str(e)}")
                        # Fallback to original image
                        save_featured_image(template_photo, "bannerbear", article, selected_template, template_photo)

                else:
                    save_featured_image(template_photo, "bannerbear", article, selected_template, template_photo)

            # Handle AI-generated images if no featured image
            elif user.image_source == "ai_image_generation":
                try:
                    provider = "segmind" if user.feature_image_template_label == "premium" else "deepinfra"
                    ai_image_response = generate_AI_feature_image__sync(
                        article.article_uid,
                        provider
                    )
                    if ai_image_response and isinstance(ai_image_response, dict) and ai_image_response.get("image_url"):
                        save_featured_image(
                            ai_image_response["image_url"],
                            "ai_image_generation",
                            article,
                            "ai_image_generation",
                            "https://cdn.abun.com/abun-media%2Fdefault-featured-image.jpeg"
                        )

                except Exception as e:
                    logger.critical(f"Error generating AI featured image: {str(e)}")

            # Handle Unsplash images if no featured image
            elif user.image_source == "unsplash":
                try:
                    unsplash_images = get_unsplash_images(article.title, 1)
                    if unsplash_images:
                        image_data = unsplash_images[0]
                        save_featured_image(
                            image_data['urls']['regular'],
                            "defaultimage",
                            article,
                            "unsplash",
                            image_data['urls']['regular']
                        )

                except Exception as e:
                    logger.critical(f"Error getting Unsplash image: {str(e)}")

        except Exception as e:
            logger.critical(f"Error processing article content and images: {str(e)}")
            raise
